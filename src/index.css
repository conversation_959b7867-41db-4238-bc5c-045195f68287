
/* Import Geist Sans font */
@import url('https://fonts.googleapis.com/css2?family=Geist+Sans:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 0 0% 0%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --radius: 0.5rem;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Geist Sans', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

@layer components {
  /* Glass morphism effects */
  .glass-primary {
    background: rgba(0, 0, 0, 0.80);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(64, 64, 64, 0.30);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.50);
  }

  .glass-secondary {
    background: rgba(17, 17, 17, 0.60);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(64, 64, 64, 0.20);
  }

  .glass-accent {
    background: rgba(38, 38, 38, 0.40);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(64, 64, 64, 0.25);
  }

  .glass-button {
    background: rgba(38, 38, 38, 0.40);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(107, 114, 128, 0.3);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(55, 55, 55, 0.60);
    border-color: rgba(156, 163, 175, 0.5);
    transform: translateY(-1px);
  }

  .glass-button:focus-visible {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
  }

  /* Effects */
  .glow {
    box-shadow: 0 0 20px rgba(156, 163, 175, 0.30), 0 0 40px rgba(156, 163, 175, 0.10);
  }

  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }

  .floating {
    animation: float 4s ease-in-out infinite;
  }

  .divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.08);
  }

  .progress-bar {
    background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
    height: 4px;
    border-radius: 2px;
  }

  .feature-card {
    transition: all 0.3s ease;
  }

  .feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
  }
}

@layer utilities {
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-2px);
    }
  }
}
