
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock,
  Target,
  Shield,
  FileCheck,
  AlertTriangle
} from "lucide-react";

const ComplianceTracker = () => {
  const complianceCategories = [
    {
      name: "Technical Requirements",
      items: 24,
      completed: 22,
      score: 92,
      status: "good",
      requirements: [
        { id: "TR-001", name: "System Architecture", status: "completed", mandatory: true },
        { id: "TR-002", name: "Security Standards", status: "completed", mandatory: true },
        { id: "TR-003", name: "Performance Metrics", status: "completed", mandatory: true },
        { id: "TR-004", name: "Integration Requirements", status: "in-progress", mandatory: true },
        { id: "TR-005", name: "Backup & Recovery", status: "pending", mandatory: false }
      ]
    },
    {
      name: "Commercial Requirements",
      items: 18,
      completed: 16,
      score: 89,
      status: "good",
      requirements: [
        { id: "CR-001", name: "Pricing Structure", status: "completed", mandatory: true },
        { id: "CR-002", name: "Payment Terms", status: "completed", mandatory: true },
        { id: "CR-003", name: "Warranty Details", status: "completed", mandatory: true },
        { id: "CR-004", name: "Service Level Agreement", status: "in-progress", mandatory: true },
        { id: "CR-005", name: "Maintenance Costs", status: "pending", mandatory: false }
      ]
    },
    {
      name: "Legal & Compliance",
      items: 15,
      completed: 13,
      score: 87,
      status: "warning",
      requirements: [
        { id: "LC-001", name: "Company Registration", status: "completed", mandatory: true },
        { id: "LC-002", name: "Insurance Coverage", status: "completed", mandatory: true },
        { id: "LC-003", name: "Security Clearance", status: "completed", mandatory: true },
        { id: "LC-004", name: "Compliance Certificates", status: "in-progress", mandatory: true },
        { id: "LC-005", name: "Data Protection Policy", status: "pending", mandatory: true }
      ]
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-blue-500" />;
      case "pending":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: "bg-green-100 text-green-700",
      "in-progress": "bg-blue-100 text-blue-700",
      pending: "bg-orange-100 text-orange-700"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-700"}>
        {status.replace("-", " ")}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Compliance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overall Score</p>
                <p className="text-2xl font-bold text-green-600">94%</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Mandatory Items</p>
                <p className="text-2xl font-bold text-blue-600">45/48</p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600">51/57</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-orange-600">6</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Compliance Tracking */}
      <Tabs defaultValue="technical" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="technical">Technical</TabsTrigger>
          <TabsTrigger value="commercial">Commercial</TabsTrigger>
          <TabsTrigger value="legal">Legal & Compliance</TabsTrigger>
        </TabsList>

        {complianceCategories.map((category, categoryIndex) => (
          <TabsContent 
            key={categoryIndex} 
            value={category.name.toLowerCase().replace(" & ", "").replace(" ", "")}
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      <FileCheck className="h-5 w-5" />
                      <span>{category.name}</span>
                    </CardTitle>
                    <CardDescription>
                      {category.completed} of {category.items} requirements completed
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      {category.score}%
                    </div>
                    <Progress value={(category.completed / category.items) * 100} className="w-32" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.requirements.map((req, reqIndex) => (
                    <div 
                      key={reqIndex} 
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(req.status)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{req.name}</span>
                            {req.mandatory && (
                              <Badge variant="outline" className="text-xs">
                                Mandatory
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-500">{req.id}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(req.status)}
                        <Button variant="ghost" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Action Panel */}
      <Card>
        <CardHeader>
          <CardTitle>Compliance Actions</CardTitle>
          <CardDescription>
            Recommended actions to improve compliance score
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button>
              <FileCheck className="h-4 w-4 mr-2" />
              Run Full Compliance Check
            </Button>
            <Button variant="outline">
              Generate Compliance Report
            </Button>
            <Button variant="outline">
              Export Requirements Matrix
            </Button>
            <Button variant="outline">
              Schedule Review Meeting
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ComplianceTracker;
