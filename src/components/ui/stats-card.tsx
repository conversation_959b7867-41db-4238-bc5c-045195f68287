import React from "react";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: LucideIcon;
  trend?: {
    value: number;
    direction: "up" | "down";
  };
  className?: string;
  iconColor?: string;
}

export function StatsCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  className,
  iconColor = "text-primary"
}: StatsCardProps) {
  return (
    <div className={cn("glass-primary rounded-xl p-6 feature-card", className)}>
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <p className="text-sm text-muted-foreground font-light uppercase tracking-wide">
            {title}
          </p>
          <p className="text-2xl font-medium text-glow">
            {value}
          </p>
          {subtitle && (
            <p className="text-xs text-muted-foreground">
              {subtitle}
            </p>
          )}
          {trend && (
            <div className={cn(
              "inline-flex items-center gap-1 text-xs px-2 py-1 rounded-full",
              trend.direction === "up" 
                ? "text-green-400 bg-green-400/10" 
                : "text-red-400 bg-red-400/10"
            )}>
              <span>{trend.direction === "up" ? "↗" : "↘"}</span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
        {Icon && (
          <div className={cn("glow", iconColor)}>
            <Icon className="h-8 w-8" />
          </div>
        )}
      </div>
    </div>
  );
}