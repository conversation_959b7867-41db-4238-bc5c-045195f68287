import React from "react";
import { cn } from "@/lib/utils";

interface LiquidGlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "hover" | "interactive";
  children: React.ReactNode;
}

export function LiquidGlassCard({ 
  variant = "default", 
  className, 
  children,
  ...props 
}: LiquidGlassCardProps) {
  return (
    <div
      className={cn(
        "relative overflow-hidden rounded-2xl",
        "glass-primary border border-white/10",
        "backdrop-blur-xl backdrop-saturate-150",
        variant === "hover" && "transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-white/10",
        variant === "interactive" && "cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-white/10 active:scale-[0.98]",
        className
      )}
      {...props}
    >
      {/* Liquid glass effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent pointer-events-none" />
      <div className="absolute inset-0 bg-gradient-to-tl from-primary/5 via-transparent to-transparent pointer-events-none" />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}