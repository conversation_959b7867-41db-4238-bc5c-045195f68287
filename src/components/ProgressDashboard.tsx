
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  Clock, 
  TrendingUp, 
  Users, 
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON>gle,
  BarChart3,
  Target
} from "lucide-react";

const ProgressDashboard = () => {
  const timeline = [
    {
      phase: "Project Initiation",
      startDate: "2024-05-01",
      endDate: "2024-05-15",
      progress: 100,
      status: "completed",
      tasks: [
        { name: "Requirements Analysis", completed: true },
        { name: "Team Assignment", completed: true },
        { name: "Project Plan", completed: true }
      ]
    },
    {
      phase: "Technical Development",
      startDate: "2024-05-16",
      endDate: "2024-06-30",
      progress: 85,
      status: "in-progress",
      tasks: [
        { name: "Architecture Design", completed: true },
        { name: "Security Framework", completed: true },
        { name: "Performance Analysis", completed: false },
        { name: "Integration Plan", completed: false }
      ]
    },
    {
      phase: "Commercial Preparation",
      startDate: "2024-06-15",
      endDate: "2024-07-05",
      progress: 70,
      status: "in-progress",
      tasks: [
        { name: "Cost Analysis", completed: true },
        { name: "Pricing Strategy", completed: true },
        { name: "Contract Terms", completed: false },
        { name: "Financial Model", completed: false }
      ]
    },
    {
      phase: "Quality Assurance",
      startDate: "2024-07-01",
      endDate: "2024-07-10",
      progress: 30,
      status: "in-progress",
      tasks: [
        { name: "Document Review", completed: false },
        { name: "Compliance Check", completed: false },
        { name: "Final Validation", completed: false }
      ]
    },
    {
      phase: "Submission",
      startDate: "2024-07-10",
      endDate: "2024-07-15",
      progress: 0,
      status: "pending",
      tasks: [
        { name: "Final Assembly", completed: false },
        { name: "Package Creation", completed: false },
        { name: "Submission", completed: false }
      ]
    }
  ];

  const teamProgress = [
    { name: "Sarah Chen", role: "Technical Lead", tasksCompleted: 12, tasksTotal: 15, completion: 80 },
    { name: "Mike Roberts", role: "Security Specialist", tasksCompleted: 8, tasksTotal: 10, completion: 80 },
    { name: "Emma Wilson", role: "Systems Analyst", tasksCompleted: 9, tasksTotal: 12, completion: 75 },
    { name: "David Kim", role: "Commercial Manager", tasksCompleted: 6, tasksTotal: 8, completion: 75 },
    { name: "Lisa Park", role: "Project Manager", tasksCompleted: 14, tasksTotal: 16, completion: 88 }
  ];

  const upcomingMilestones = [
    { name: "Financial Proposal Completion", date: "2024-07-05", daysLeft: 3, priority: "high" },
    { name: "Quality Review Meeting", date: "2024-07-08", daysLeft: 6, priority: "medium" },
    { name: "Final Document Assembly", date: "2024-07-12", daysLeft: 10, priority: "high" },
    { name: "Submission Deadline", date: "2024-07-15", daysLeft: 13, priority: "critical" }
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: "bg-green-100 text-green-700",
      "in-progress": "bg-blue-100 text-blue-700",
      pending: "bg-gray-100 text-gray-700"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-700"}>
        {status.replace("-", " ")}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      critical: "bg-red-100 text-red-700",
      high: "bg-orange-100 text-orange-700",
      medium: "bg-yellow-100 text-yellow-700",
      low: "bg-blue-100 text-blue-700"
    };
    
    return (
      <Badge className={variants[priority as keyof typeof variants] || "bg-gray-100 text-gray-700"}>
        {priority}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overall Progress</p>
                <p className="text-2xl font-bold text-blue-600">67%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Tasks Complete</p>
                <p className="text-2xl font-bold text-green-600">49/73</p>
              </div>
              <CheckCircle2 className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Days Remaining</p>
                <p className="text-2xl font-bold text-orange-600">13</p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Team</p>
                <p className="text-2xl font-bold text-purple-600">5</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Project Timeline</span>
            </CardTitle>
            <CardDescription>
              Progress across major project phases
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {timeline.map((phase, index) => (
                <div key={index} className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{phase.phase}</h4>
                    {getStatusBadge(phase.status)}
                  </div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Progress value={phase.progress} className="flex-1" />
                    <span className="text-sm font-medium w-12">{phase.progress}%</span>
                  </div>
                  <div className="text-sm text-gray-500 mb-3">
                    {phase.startDate} → {phase.endDate}
                  </div>
                  <div className="pl-4 border-l-2 border-gray-200">
                    {phase.tasks.map((task, taskIndex) => (
                      <div key={taskIndex} className="flex items-center space-x-2 py-1">
                        <CheckCircle2 
                          className={`h-3 w-3 ${task.completed ? 'text-green-500' : 'text-gray-300'}`} 
                        />
                        <span className={`text-sm ${task.completed ? 'text-gray-900' : 'text-gray-500'}`}>
                          {task.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Team Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Team Progress</span>
            </CardTitle>
            <CardDescription>
              Individual team member contributions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {teamProgress.map((member, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{member.name}</h4>
                      <p className="text-sm text-gray-500">{member.role}</p>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-sm">{member.completion}%</div>
                      <div className="text-xs text-gray-500">
                        {member.tasksCompleted}/{member.tasksTotal} tasks
                      </div>
                    </div>
                  </div>
                  <Progress value={member.completion} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Milestones */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Upcoming Milestones</span>
          </CardTitle>
          <CardDescription>
            Key deadlines and deliverables
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {upcomingMilestones.map((milestone, index) => (
              <div 
                key={index} 
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium">{milestone.name}</p>
                    <p className="text-sm text-gray-500">Due: {milestone.date}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="font-bold text-sm">
                      {milestone.daysLeft} days left
                    </div>
                  </div>
                  {getPriorityBadge(milestone.priority)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Progress Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Progress Management</CardTitle>
          <CardDescription>
            Tools to track and manage project progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button>
              <TrendingUp className="h-4 w-4 mr-2" />
              Generate Progress Report
            </Button>
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Update Timeline
            </Button>
            <Button variant="outline">
              <Users className="h-4 w-4 mr-2" />
              Assign Tasks
            </Button>
            <Button variant="outline">
              Export Timeline
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProgressDashboard;
