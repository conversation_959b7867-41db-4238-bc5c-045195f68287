
import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import TenderCard from "./TenderCard";
import WorkflowStepper from "./WorkflowStepper";
import AIGenerateButton from "./AIGenerateButton";
import { 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle2, 
  FileText,
  Calendar,
  Users,
  Target
} from "lucide-react";

const ProjectOverview = () => {
  const mockTenders = [
    {
      tenderId: "ARAPS-2024-DCT-001",
      name: "Defense Communications Infrastructure",
      status: "in-progress" as const,
      dueDate: "2024-07-15",
      kpiValues: { completion: 87, compliance: 94, quality: 91 }
    },
    {
      tenderId: "ARAPS-2024-CYB-002", 
      name: "Cybersecurity Framework Implementation",
      status: "review" as const,
      dueDate: "2024-08-01",
      kpiValues: { completion: 95, compliance: 98, quality: 88 }
    }
  ];

  const workflowSteps = [
    { id: "1", title: "Requirements Analysis", status: "completed" as const, description: "Technical specifications reviewed" },
    { id: "2", title: "Solution Design", status: "completed" as const, description: "Architecture blueprint finalized" },
    { id: "3", title: "Compliance Review", status: "current" as const, description: "Security clearance validation" },
    { id: "4", title: "Final Assembly", status: "pending" as const, description: "Document compilation pending" },
    { id: "5", title: "Submission", status: "pending" as const, description: "Awaiting final review" }
  ];

  const recentActivity = [
    { action: "Document uploaded", item: "Technical Architecture v2.1", time: "2 hours ago", user: "Sarah Chen" },
    { action: "Compliance check", item: "Security Requirements", time: "4 hours ago", user: "System" },
    { action: "Review completed", item: "Project Timeline", time: "6 hours ago", user: "Mike Roberts" },
    { action: "Template updated", item: "Risk Assessment Matrix", time: "1 day ago", user: "Emma Wilson" }
  ];

  const handleAIGenerate = async () => {
    console.log("Generating AI content...");
    // Simulate AI generation
    await new Promise(resolve => setTimeout(resolve, 2000));
  };

  return (
    <div className="space-y-8">
      {/* Active Tenders */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium tracking-tight">ACTIVE TENDERS</h3>
          <AIGenerateButton onGenerate={handleAIGenerate} tokenCost={150} />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {mockTenders.map(tender => (
            <TenderCard key={tender.tenderId} {...tender} />
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Workflow Status */}
        <div className="lg:col-span-2">
          <WorkflowStepper steps={workflowSteps} currentStep="3" />
        </div>

        {/* Key Metrics */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium tracking-tight">KEY METRICS</h3>
          <div className="space-y-4">
            <Card className="glass-secondary border-emerald-400/20">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-emerald-300 tracking-tight">
                    SUBMISSION READINESS
                  </CardTitle>
                  <Target className="h-4 w-4 text-emerald-400 glow" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-medium text-emerald-400 text-glow mb-2">87%</div>
                <Progress value={87} className="progress-bar h-2" />
                <p className="text-xs text-gray-400 mt-2">3 sections pending</p>
              </CardContent>
            </Card>

            <Card className="glass-secondary border-blue-400/20">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-blue-300 tracking-tight">
                    COMPLIANCE SCORE
                  </CardTitle>
                  <CheckCircle2 className="h-4 w-4 text-blue-400 glow" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-medium text-blue-400 text-glow mb-2">94%</div>
                <Progress value={94} className="h-2" />
                <p className="text-xs text-gray-400 mt-2">All requirements met</p>
              </CardContent>
            </Card>

            <Card className="glass-secondary border-purple-400/20">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-purple-300 tracking-tight">
                    QUALITY RATING
                  </CardTitle>
                  <TrendingUp className="h-4 w-4 text-purple-400 glow" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-medium text-purple-400 text-glow mb-2">91%</div>
                <Progress value={91} className="h-2" />
                <p className="text-xs text-gray-400 mt-2">Above target threshold</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="glass-primary rounded-xl p-6">
        <h3 className="text-lg font-medium tracking-tight mb-6">RECENT ACTIVITY</h3>
        <div className="space-y-4">
          {recentActivity.map((activity, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 glass-secondary rounded-lg transition-all hover:glass-accent">
              <div className="flex-shrink-0 mt-1">
                <div className="h-2 w-2 rounded-full bg-blue-400 glow" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm">
                  <span className="font-medium text-blue-300">{activity.action}:</span>{" "}
                  <span className="text-gray-300">{activity.item}</span>
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  {activity.time} by {activity.user}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Alerts & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="glass-primary rounded-xl p-6">
          <h3 className="text-lg font-medium tracking-tight mb-6 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-400 glow" />
            ALERTS & WARNINGS
          </h3>
          <div className="space-y-3">
            <div className="glass-secondary rounded-lg p-4 border-l-4 border-yellow-400">
              <p className="text-sm font-medium text-yellow-300">MISSING DOCUMENTATION</p>
              <p className="text-xs text-gray-400 mt-1">
                3 mandatory documents pending upload
              </p>
            </div>
            
            <div className="glass-secondary rounded-lg p-4 border-l-4 border-red-400">
              <p className="text-sm font-medium text-red-300">DEADLINE ALERT</p>
              <p className="text-xs text-gray-400 mt-1">
                Financial proposal due in 3 days
              </p>
            </div>
          </div>
        </div>

        <div className="glass-primary rounded-xl p-6">
          <h3 className="text-lg font-medium tracking-tight mb-6">QUICK ACTIONS</h3>
          <div className="space-y-2">
            <Button className="w-full glass-button hover:glass-button border-emerald-400/30" size="sm">
              GENERATE REPORT
            </Button>
            <Button className="w-full glass-button hover:glass-button border-blue-400/30" size="sm">
              RUN COMPLIANCE CHECK  
            </Button>
            <Button className="w-full glass-button hover:glass-button border-purple-400/30" size="sm">
              EXPORT DOCUMENTS
            </Button>
            <Button className="w-full glass-button hover:glass-button border-orange-400/30" size="sm">
              SCHEDULE REVIEW
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectOverview;
