import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock, AlertCircle, CheckCircle } from "lucide-react";

interface TenderCardProps {
  tenderId: string;
  name: string;
  status: "draft" | "in-progress" | "review" | "submitted";
  dueDate: string;
  kpiValues: {
    completion: number;
    compliance: number;
    quality: number;
  };
  variant?: "small" | "medium";
}

const statusConfig = {
  draft: { color: "glass-accent bg-gray-500/20 text-gray-300 border-gray-400/30", icon: Clock },
  "in-progress": { color: "glass-accent bg-blue-500/20 text-blue-300 border-blue-400/30", icon: Clock },
  review: { color: "glass-accent bg-orange-500/20 text-orange-300 border-orange-400/30", icon: AlertCircle },
  submitted: { color: "glass-accent bg-emerald-500/20 text-emerald-300 border-emerald-400/30", icon: CheckCircle }
};

export default function TenderCard({ 
  tenderId, 
  name, 
  status, 
  dueDate, 
  kpiValues, 
  variant = "medium" 
}: TenderCardProps) {
  const StatusIcon = statusConfig[status].icon;
  const isSmall = variant === "small";

  return (
    <Card className={`glass-primary feature-card ${isSmall ? 'p-4' : 'p-6'}`}>
      <CardHeader className={isSmall ? 'pb-3' : 'pb-4'}>
        <div className="flex items-center justify-between">
          <CardTitle className={`${isSmall ? 'text-base' : 'text-lg'} font-medium tracking-tight`}>
            {name}
          </CardTitle>
          <Badge className={statusConfig[status].color}>
            <StatusIcon className="h-3 w-3 mr-1" />
            {status.replace('-', ' ')}
          </Badge>
        </div>
        <CardDescription className="text-xs text-gray-400">
          ID: {tenderId} • Due: {dueDate}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div>
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-400">Completion</span>
              <span className="text-emerald-400">{kpiValues.completion}%</span>
            </div>
            <Progress value={kpiValues.completion} className="h-2" />
          </div>
          
          <div>
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-400">Compliance</span>
              <span className="text-blue-400">{kpiValues.compliance}%</span>
            </div>
            <Progress value={kpiValues.compliance} className="h-2" />
          </div>
          
          <div>
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-400">Quality</span>
              <span className="text-purple-400">{kpiValues.quality}%</span>
            </div>
            <Progress value={kpiValues.quality} className="h-2" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}