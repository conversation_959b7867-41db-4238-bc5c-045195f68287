
import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  FileText, 
  Upload, 
  Download, 
  Eye, 
  Edit3, 
  Trash2, 
  Search,
  Filter,
  FolderOpen,
  Clock,
  User,
  CheckCircle2
} from "lucide-react";

const DocumentManager = () => {
  const [searchTerm, setSearchTerm] = useState("");
  
  const documentCategories = [
    {
      name: "Technical Documents",
      count: 12,
      documents: [
        {
          name: "System Architecture Diagram",
          type: "PDF",
          size: "2.4 MB",
          version: "v2.1",
          status: "approved",
          lastModified: "2024-07-01",
          author: "<PERSON>",
          mandatory: true
        },
        {
          name: "Security Implementation Plan",
          type: "DOCX",
          size: "856 KB",
          version: "v1.3",
          status: "draft",
          lastModified: "2024-06-30",
          author: "<PERSON>",
          mandatory: true
        },
        {
          name: "Performance Test Results",
          type: "XLSX",
          size: "1.2 MB",
          version: "v1.0",
          status: "review",
          lastModified: "2024-06-28",
          author: "Emma Wilson",
          mandatory: false
        }
      ]
    },
    {
      name: "Commercial Documents",
      count: 8,
      documents: [
        {
          name: "Financial Proposal",
          type: "PDF",
          size: "3.1 MB",
          version: "v2.0",
          status: "approved",
          lastModified: "2024-07-02",
          author: "David Kim",
          mandatory: true
        },
        {
          name: "Service Level Agreement",
          type: "DOCX",
          size: "445 KB",
          version: "v1.2",
          status: "draft",
          lastModified: "2024-06-29",
          author: "Lisa Park",
          mandatory: true
        }
      ]
    },
    {
      name: "Legal & Compliance",
      count: 6,
      documents: [
        {
          name: "Company Registration Certificate",
          type: "PDF",
          size: "890 KB",
          version: "v1.0",
          status: "approved",
          lastModified: "2024-06-25",
          author: "Admin",
          mandatory: true
        },
        {
          name: "Insurance Documentation",
          type: "PDF",
          size: "1.5 MB",
          version: "v1.1",
          status: "approved",
          lastModified: "2024-06-27",
          author: "HR Team",
          mandatory: true
        }
      ]
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      approved: "bg-green-100 text-green-700",
      draft: "bg-yellow-100 text-yellow-700",
      review: "bg-blue-100 text-blue-700",
      rejected: "bg-red-100 text-red-700"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-700"}>
        {status}
      </Badge>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "review":
        return <Eye className="h-4 w-4 text-blue-500" />;
      case "draft":
        return <Edit3 className="h-4 w-4 text-yellow-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Document Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Documents</p>
                <p className="text-2xl font-bold text-blue-600">26</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-green-600">18</p>
              </div>
              <CheckCircle2 className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">In Review</p>
                <p className="text-2xl font-bold text-blue-600">5</p>
              </div>
              <Eye className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Draft</p>
                <p className="text-2xl font-bold text-yellow-600">3</p>
              </div>
              <Edit3 className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Document Library</CardTitle>
              <CardDescription>Manage all tender-related documents</CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Bulk Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>

          {/* Document Categories */}
          <div className="space-y-8">
            {documentCategories.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <div className="flex items-center space-x-2 mb-4">
                  <FolderOpen className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold">{category.name}</h3>
                  <Badge variant="outline">{category.count} documents</Badge>
                </div>
                
                <div className="space-y-2">
                  {category.documents.map((doc, docIndex) => (
                    <div 
                      key={docIndex} 
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        {getStatusIcon(doc.status)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <p className="font-medium truncate">{doc.name}</p>
                            {doc.mandatory && (
                              <Badge variant="outline" className="text-xs">
                                Mandatory
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>{doc.type} • {doc.size}</span>
                            <span className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{doc.lastModified}</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              <User className="h-3 w-3" />
                              <span>{doc.author}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <Badge variant="outline">{doc.version}</Badge>
                        {getStatusBadge(doc.status)}
                        <div className="flex space-x-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentManager;
