import { CheckCircle, Circle, Clock, AlertCircle } from "lucide-react";

interface Step {
  id: string;
  title: string;
  status: "completed" | "current" | "pending" | "blocked";
  description?: string;
}

interface WorkflowStepperProps {
  steps: Step[];
  currentStep: string;
}

const statusConfig = {
  completed: { icon: CheckCircle, color: "text-emerald-400", bg: "bg-emerald-500" },
  current: { icon: Clock, color: "text-blue-400", bg: "bg-blue-500" },
  pending: { icon: Circle, color: "text-gray-400", bg: "bg-gray-600" },
  blocked: { icon: AlertCircle, color: "text-red-400", bg: "bg-red-500" }
};

export default function WorkflowStepper({ steps, currentStep }: WorkflowStepperProps) {
  return (
    <div className="glass-primary rounded-xl p-6">
      <h3 className="text-lg font-medium tracking-tight mb-6">WORKFLOW PROGRESS</h3>
      
      <div className="space-y-4">
        {steps.map((step, index) => {
          const config = statusConfig[step.status];
          const Icon = config.icon;
          const isLast = index === steps.length - 1;
          
          return (
            <div key={step.id} className="relative">
              <div className="flex items-center gap-4">
                <div className={`w-8 h-8 rounded-full ${config.bg} flex items-center justify-center shrink-0`}>
                  <Icon className="h-4 w-4 text-white" />
                </div>
                
                <div className="flex-1">
                  <h4 className={`font-medium text-sm ${config.color}`}>
                    {step.title}
                  </h4>
                  {step.description && (
                    <p className="text-xs text-gray-400 mt-1">
                      {step.description}
                    </p>
                  )}
                </div>
              </div>
              
              {!isLast && (
                <div className="w-px h-6 bg-gray-600 ml-4 mt-2" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}