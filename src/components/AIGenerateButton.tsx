import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Sparkles, Loader2 } from "lucide-react";
import { useState } from "react";

interface AIGenerateButtonProps {
  onGenerate: () => void | Promise<void>;
  disabled?: boolean;
  tokenCost?: number;
  variant?: "primary" | "secondary";
}

export default function AIGenerateButton({ 
  onGenerate, 
  disabled = false, 
  tokenCost, 
  variant = "primary" 
}: AIGenerateButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerate = async () => {
    if (disabled || isGenerating) return;
    
    setIsGenerating(true);
    try {
      await onGenerate();
    } finally {
      setIsGenerating(false);
    }
  };

  const buttonClass = variant === "primary" 
    ? "glass-button hover:glass-button border-purple-400/30 bg-purple-500/10 hover:bg-purple-500/20" 
    : "glass-button hover:glass-button border-gray-400/30";

  return (
    <div className="flex items-center gap-2">
      <Button
        onClick={handleGenerate}
        disabled={disabled || isGenerating}
        className={`${buttonClass} transition-all duration-300`}
        size="sm"
      >
        {isGenerating ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <Sparkles className="h-4 w-4 mr-2 glow" />
        )}
        {isGenerating ? "GENERATING..." : "AI GENERATE"}
      </Button>
      
      {tokenCost && (
        <Badge className="glass-accent text-xs text-purple-300 border-purple-400/30">
          ~{tokenCost} tokens
        </Badge>
      )}
    </div>
  );
}