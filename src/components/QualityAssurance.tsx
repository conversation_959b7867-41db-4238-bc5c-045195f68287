
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCw,
  FileCheck,
  Bug,
  Zap,
  Target,
  TrendingUp
} from "lucide-react";

const QualityAssurance = () => {
  const qualityChecks = [
    {
      category: "Content Quality",
      score: 92,
      checks: [
        { name: "Grammar & Spelling", status: "passed", score: 98, issues: 2 },
        { name: "Technical Accuracy", status: "passed", score: 95, issues: 3 },
        { name: "Completeness", status: "warning", score: 87, issues: 8 },
        { name: "Consistency", status: "passed", score: 91, issues: 5 }
      ]
    },
    {
      category: "Format & Structure",
      score: 89,
      checks: [
        { name: "Document Formatting", status: "passed", score: 94, issues: 4 },
        { name: "Section Structure", status: "passed", score: 92, issues: 2 },
        { name: "Cross-references", status: "failed", score: 78, issues: 12 },
        { name: "Table of Contents", status: "passed", score: 96, issues: 1 }
      ]
    },
    {
      category: "Compliance",
      score: 95,
      checks: [
        { name: "Requirement Coverage", status: "passed", score: 97, issues: 2 },
        { name: "Mandatory Elements", status: "passed", score: 100, issues: 0 },
        { name: "Word Count Limits", status: "warning", score: 88, issues: 6 },
        { name: "Template Adherence", status: "passed", score: 93, issues: 4 }
      ]
    }
  ];

  const recentIssues = [
    {
      id: "QA-001",
      severity: "high",
      category: "Cross-references",
      description: "Missing reference to Appendix C in Section 3.2",
      document: "Technical Response",
      assignee: "Sarah Chen",
      status: "open"
    },
    {
      id: "QA-002",
      severity: "medium",
      category: "Word Count",
      description: "Executive Summary exceeds 500-word limit by 23 words",
      document: "Executive Summary",
      assignee: "Mike Roberts",
      status: "in-progress"
    },
    {
      id: "QA-003",
      severity: "low",
      category: "Formatting",
      description: "Inconsistent heading styles in Section 4",
      document: "Commercial Proposal",
      assignee: "Emma Wilson",
      status: "resolved"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "passed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      passed: "bg-green-100 text-green-700",
      warning: "bg-yellow-100 text-yellow-700",
      failed: "bg-red-100 text-red-700"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-700"}>
        {status}
      </Badge>
    );
  };

  const getSeverityBadge = (severity: string) => {
    const variants = {
      high: "bg-red-100 text-red-700",
      medium: "bg-yellow-100 text-yellow-700",
      low: "bg-blue-100 text-blue-700"
    };
    
    return (
      <Badge className={variants[severity as keyof typeof variants] || "bg-gray-100 text-gray-700"}>
        {severity}
      </Badge>
    );
  };

  const getIssueStatusBadge = (status: string) => {
    const variants = {
      open: "bg-red-100 text-red-700",
      "in-progress": "bg-blue-100 text-blue-700",
      resolved: "bg-green-100 text-green-700"
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-700"}>
        {status.replace("-", " ")}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Quality Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overall Score</p>
                <p className="text-2xl font-bold text-green-600">92%</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Checks Passed</p>
                <p className="text-2xl font-bold text-green-600">9/12</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Open Issues</p>
                <p className="text-2xl font-bold text-orange-600">15</p>
              </div>
              <Bug className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Last Check</p>
                <p className="text-2xl font-bold text-blue-600">2h ago</p>
              </div>
              <RefreshCw className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="checks" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="checks">Quality Checks</TabsTrigger>
          <TabsTrigger value="issues">Issues & Fixes</TabsTrigger>
        </TabsList>

        <TabsContent value="checks">
          <div className="space-y-4">
            {qualityChecks.map((category, categoryIndex) => (
              <Card key={categoryIndex}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <FileCheck className="h-5 w-5" />
                        <span>{category.category}</span>
                      </CardTitle>
                      <CardDescription>
                        Quality assessment for {category.category.toLowerCase()}
                      </CardDescription>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {category.score}%
                      </div>
                      <Progress value={category.score} className="w-32" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {category.checks.map((check, checkIndex) => (
                      <div 
                        key={checkIndex} 
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(check.status)}
                          <div>
                            <p className="font-medium">{check.name}</p>
                            <p className="text-sm text-gray-500">
                              {check.issues} issues found
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            <div className="font-bold text-sm">{check.score}%</div>
                          </div>
                          {getStatusBadge(check.status)}
                          <Button variant="ghost" size="sm">
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="issues">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bug className="h-5 w-5" />
                <span>Quality Issues</span>
              </CardTitle>
              <CardDescription>
                Track and resolve quality issues across all documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentIssues.map((issue, issueIndex) => (
                  <div 
                    key={issueIndex} 
                    className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-mono text-sm text-gray-500">{issue.id}</span>
                        {getSeverityBadge(issue.severity)}
                        <Badge variant="outline">{issue.category}</Badge>
                      </div>
                      <p className="font-medium mb-1">{issue.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>Document: {issue.document}</span>
                        <span>Assigned to: {issue.assignee}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getIssueStatusBadge(issue.status)}
                      <Button variant="ghost" size="sm">
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Panel */}
      <Card>
        <CardHeader>
          <CardTitle>Quality Actions</CardTitle>
          <CardDescription>
            Tools to improve and maintain document quality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button>
              <RefreshCw className="h-4 w-4 mr-2" />
              Run Full Quality Check
            </Button>
            <Button variant="outline">
              <TrendingUp className="h-4 w-4 mr-2" />
              Generate Quality Report
            </Button>
            <Button variant="outline">
              <Zap className="h-4 w-4 mr-2" />
              Auto-fix Issues
            </Button>
            <Button variant="outline">
              Export Issue List
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QualityAssurance;
